import type { SearchRequest, SearchResponse, SearchResult, SearchProcessStep } from '@/types';

export class SearchService {
  private static instance: SearchService;

  static getInstance(): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService();
    }
    return SearchService.instance;
  }

  // 执行搜索
  async executeSearch(request: SearchRequest): Promise<SearchResponse> {
    try {
      const response = await fetch('http://localhost:8000/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: request.query,
          workspaceId: request.workspaceId,
          searchType: request.searchType,
          maxResults: request.options?.maxResults || 50,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      return {
        searchId: result.searchId,
        query: result.query,
        result: result.result,
        searchTime: result.searchTime,
      };
    } catch (error) {
      throw new Error(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 获取搜索状态（用于实时更新）
  async getSearchStatus(searchId: string): Promise<{ status: string; progress: number }> {
    // 在实际应用中，这里会查询搜索进度
    return {
      status: 'completed',
      progress: 100,
    };
  }
}

export const searchService = SearchService.getInstance();
