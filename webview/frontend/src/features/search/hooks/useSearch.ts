import { useState, useCallback } from 'react';
import { searchService } from '@/services/searchService';
import type { SearchRequest, SearchResponse } from '@/types';

export function useSearch() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastQuery, setLastQuery] = useState<string>('');
  const [searchTime, setSearchTime] = useState<number>(0);

  // 执行搜索
  const executeSearch = useCallback(async (request: SearchRequest) => {
    setLoading(true);
    setError(null);
    setLastQuery(request.query);

    try {
      const response: SearchResponse = await searchService.executeSearch(request);
      setResult(response.result);
      setSearchTime(response.searchTime);
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败');
      setResult('');
    } finally {
      setLoading(false);
    }
  }, []);

  // 清空搜索结果
  const clearResults = useCallback(() => {
    setResult('');
    setError(null);
    setLastQuery('');
    setSearchTime(0);
  }, []);

  return {
    result,
    loading,
    error,
    lastQuery,
    searchTime,
    executeSearch,
    clearResults,
  };
}
