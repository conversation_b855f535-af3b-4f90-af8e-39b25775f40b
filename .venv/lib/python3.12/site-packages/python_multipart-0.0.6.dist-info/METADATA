Metadata-Version: 2.1
Name: python-multipart
Version: 0.0.6
Summary: A streaming multipart parser for Python
Project-URL: Homepage, https://github.com/andrew-d/python-multipart
Project-URL: Documentation, https://andrew-d.github.io/python-multipart/
Project-URL: Changelog, https://github.com/andrew-d/python-multipart/tags
Project-URL: Source, https://github.com/andrew-d/python-multipart
Author-email: <PERSON> <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE.txt
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Provides-Extra: dev
Requires-Dist: atomicwrites==1.2.1; extra == 'dev'
Requires-Dist: attrs==19.2.0; extra == 'dev'
Requires-Dist: coverage==6.5.0; extra == 'dev'
Requires-Dist: hatch; extra == 'dev'
Requires-Dist: invoke==1.7.3; extra == 'dev'
Requires-Dist: more-itertools==4.3.0; extra == 'dev'
Requires-Dist: pbr==4.3.0; extra == 'dev'
Requires-Dist: pluggy==1.0.0; extra == 'dev'
Requires-Dist: py==1.11.0; extra == 'dev'
Requires-Dist: pytest-cov==4.0.0; extra == 'dev'
Requires-Dist: pytest-timeout==2.1.0; extra == 'dev'
Requires-Dist: pytest==7.2.0; extra == 'dev'
Requires-Dist: pyyaml==5.1; extra == 'dev'
Description-Content-Type: text/x-rst

==================
 Python-Multipart
==================

.. image:: https://github.com/andrew-d/python-multipart/actions/workflows/test.yaml/badge.svg
        :target: https://github.com/andrew-d/python-multipart/actions


python-multipart is an Apache2 licensed streaming multipart parser for Python.
Test coverage is currently 100%.
Documentation is available `here`_.

.. _here: https://andrew-d.github.io/python-multipart/

Why?
----

Because streaming uploads are awesome for large files.

How to Test
-----------

If you want to test:

.. code-block:: bash

    $ pip install .[dev]
    $ inv test
