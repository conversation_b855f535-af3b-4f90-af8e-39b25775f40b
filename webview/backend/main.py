"""
WebView Backend API

提供三个核心接口：
1. 读取工作区目录
2. 读取文件内容  
3. 处理用户搜索请求
"""

from pathlib import Path
from typing import List, Dict, Optional
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

app = FastAPI(title="WebView Backend API", version="1.0.0")

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5174", "http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置路径
REPOS_PATH = Path(__file__).parent.parent / "repos"

# 数据模型
class WorkspaceInfo(BaseModel):
    id: str
    name: str
    path: str
    description: str = ""

class FileNode(BaseModel):
    id: str
    name: str
    type: str  # 'file' or 'directory'
    path: str
    size: Optional[int] = None
    lastModified: Optional[str] = None
    children: Optional[List['FileNode']] = None
    isExpanded: bool = False

class SearchRequest(BaseModel):
    query: str
    workspaceId: str
    searchType: str = "grep"  # 'grep' or 'embedding'
    maxResults: int = 50


class SearchResponse(BaseModel):
    searchId: str
    query: str
    result: str
    searchTime: float

# 工具函数
def get_file_size(file_path: Path) -> int:
    """获取文件大小"""
    try:
        return file_path.stat().st_size
    except:
        return 0

def get_last_modified(file_path: Path) -> str:
    """获取文件最后修改时间"""
    try:
        return file_path.stat().st_mtime.__str__()
    except:
        return ""

def should_ignore_path(path: Path) -> bool:
    """判断是否应该忽略某个路径"""
    ignore_patterns = {
        '.git', '.svn', '.hg',
        'node_modules', '__pycache__', '.pytest_cache',
        'target', 'build', 'dist', '.next',
        '.vscode', '.idea', '.DS_Store',
        '*.pyc', '*.pyo', '*.pyd',
        '.env', '.env.local'
    }
    
    name = path.name
    return (
        name.startswith('.') or
        name in ignore_patterns or
        any(name.endswith(pattern.replace('*', '')) for pattern in ignore_patterns if '*' in pattern)
    )

def build_file_tree(dir_path: Path, workspace_id: str = "", max_depth: int = 10, current_depth: int = 0) -> List[FileNode]:
    """构建文件树"""
    if current_depth >= max_depth:
        return []

    nodes = []
    try:
        # 自定义排序：目录在前，文件在后，然后按名称字母顺序排序
        def sort_key(item):
            # 目录返回 (0, 名称)，文件返回 (1, 名称)
            return (0 if item.is_dir() else 1, item.name.lower())

        for item in sorted(dir_path.iterdir(), key=sort_key):
            if should_ignore_path(item):
                continue

            # 计算相对路径
            if workspace_id:
                relative_path = str(item.relative_to(dir_path.parent))
            else:
                relative_path = item.name

            node = FileNode(
                id=relative_path,
                name=item.name,
                type="directory" if item.is_dir() else "file",
                path=str(item),
                relativePath=relative_path,
                isExpanded=False
            )

            if item.is_file():
                node.size = get_file_size(item)
                node.lastModified = get_last_modified(item)
            elif item.is_dir():
                # 递归获取子目录
                node.children = build_file_tree(
                    item, workspace_id, max_depth, current_depth + 1
                )

            nodes.append(node)
    except PermissionError:
        pass

    return nodes

# API 接口

@app.get("/")
async def root():
    """根路径"""
    return {"message": "WebView Backend API", "version": "1.0.0"}

@app.get("/api/workspaces", response_model=List[WorkspaceInfo])
async def get_workspaces():
    """获取所有工作区"""
    workspaces = []
    
    if not REPOS_PATH.exists():
        raise HTTPException(status_code=404, detail="Repos directory not found")
    
    try:
        for item in REPOS_PATH.iterdir():
            if item.is_dir() and not should_ignore_path(item):
                description = f"{item.name} repository"
                
                workspace = WorkspaceInfo(
                    id=item.name,
                    name=item.name,
                    path=str(REPOS_PATH / item.name),
                    description=description
                )
                workspaces.append(workspace)
        
        return workspaces
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read workspaces: {str(e)}")

@app.get("/api/workspaces/{workspace_name}/files", response_model=List[FileNode])
async def get_workspace_files(workspace_name: str):
    """获取工作区文件树"""
    workspace_path = REPOS_PATH / workspace_name

    if not workspace_path.exists():
        raise HTTPException(status_code=404, detail="Workspace not found")

    if not workspace_path.is_dir():
        raise HTTPException(status_code=400, detail="Workspace is not a directory")
    
    try:
        file_tree = build_file_tree(workspace_path, workspace_name)
        return file_tree
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file tree: {str(e)}")

@app.get("/api/files")
async def get_file_content(file_path: str = Query(..., description="File path relative to repos")):
    """获取文件内容"""
    # 安全检查：确保路径在 repos 目录内
    try:
        print(f"DEBUG: Original file_path: {file_path}")

        # 处理绝对路径：如果是绝对路径且包含 repos 目录，提取相对部分
        if file_path.startswith('/') and '/repos/' in file_path:
            # 提取 repos 目录之后的部分
            repos_index = file_path.find('/repos/') + len('/repos/')
            clean_path = file_path[repos_index:]
            print(f"DEBUG: Extracted clean_path from absolute path: {clean_path}")
        else:
            # 移除可能的 ../repos/ 前缀
            clean_path = file_path.replace("../repos/", "").replace("repos/", "")
            print(f"DEBUG: Cleaned relative path: {clean_path}")

        full_path = REPOS_PATH / clean_path
        print(f"DEBUG: Full path: {full_path}")
        print(f"DEBUG: REPOS_PATH: {REPOS_PATH}")

        # 确保路径在 repos 目录内
        full_path = full_path.resolve()
        repos_path_resolved = REPOS_PATH.resolve()

        if not str(full_path).startswith(str(repos_path_resolved)):
            raise HTTPException(status_code=403, detail="Access denied")

        if not full_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        if not full_path.is_file():
            raise HTTPException(status_code=400, detail="Path is not a file")
        
        # 读取文件内容
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {"content": content}
        except UnicodeDecodeError:
            # 如果是二进制文件，返回提示信息
            return {"content": f"Binary file: {full_path.name}\nFile size: {get_file_size(full_path)} bytes"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file: {str(e)}")

@app.post("/api/search", response_model=SearchResponse)
async def search_code(request: SearchRequest):
    """处理代码搜索请求"""
    workspace_path = REPOS_PATH / request.workspaceId

    if not workspace_path.exists():
        raise HTTPException(status_code=404, detail="Workspace not found")

    import time
    import asyncio

    start_time = time.time()

    try:
        # 构建命令
        project_root = Path(__file__).parent.parent.parent
        main_py_path = project_root / "main.py"
        python_path = project_root / ".venv" / "bin" / "python"

        # 如果虚拟环境不存在，使用系统Python
        if not python_path.exists():
            python_path = "python"

        cmd = [
            str(python_path),
            str(main_py_path),
            request.query,
            "--repo-path", str(workspace_path),
            "--search-type", request.searchType,
            "--verbose"
        ]

        print(f"执行命令: {' '.join(cmd)}")

        # 异步执行命令并监听输出
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=str(project_root)
        )

        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            error_msg = f"命令执行失败 (返回码: {process.returncode})\nstderr: {stderr.decode()}"
            raise Exception(error_msg)

        # 解析命令输出
        output = stdout.decode('utf-8')
        search_time = time.time() - start_time

        return SearchResponse(
            searchId=f"search_{int(time.time())}",
            query=request.query,
            result=output,
            searchTime=search_time
        )

    except Exception as e:
        import traceback
        error_detail = f"Search failed: {str(e)}\n{traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
