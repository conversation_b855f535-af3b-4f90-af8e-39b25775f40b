import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { SearchPanel } from '../webview/frontend/src/features/search/components/SearchPanel';
import type { SearchRequest } from '../webview/frontend/src/types';

// Mock the cn utility
jest.mock('../webview/frontend/src/utils/cn', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
}));

// Mock the hooks
jest.mock('../webview/frontend/src/features/workspace/hooks', () => ({
  useWorkspace: () => ({
    getCurrentWorkspace: {
      id: 'test-workspace',
      name: 'Test Workspace',
      path: '/test/path'
    }
  })
}));

jest.mock('../webview/frontend/src/store/appStore', () => ({
  useAppStore: () => ({
    searchType: 'grep' as const
  })
}));

// Mock timers
jest.useFakeTimers();

describe('SearchPanel Timer', () => {
  const mockOnSearch = jest.fn();
  const mockOnClear = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('shows timer when loading is true', async () => {
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={true}
        onClear={mockOnClear}
      />
    );

    // Timer should be visible when loading
    expect(screen.getByText(/\d+\.\d+s/)).toBeInTheDocument();
    
    // Clock icon should be visible
    const clockIcon = document.querySelector('[data-lucide="clock"]');
    expect(clockIcon).toBeInTheDocument();
  });

  it('updates timer every 100ms during search', async () => {
    const { rerender } = render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    // Initially no timer
    expect(screen.queryByText(/\d+\.\d+s/)).not.toBeInTheDocument();

    // Start loading
    rerender(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={true}
        onClear={mockOnClear}
      />
    );

    // Timer should start at 0.0s
    expect(screen.getByText('0.0s')).toBeInTheDocument();

    // Advance timer by 500ms
    act(() => {
      jest.advanceTimersByTime(500);
    });

    // Timer should show approximately 0.5s
    await waitFor(() => {
      expect(screen.getByText(/0\.[4-6]s/)).toBeInTheDocument();
    });

    // Advance timer by another 1000ms
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    // Timer should show approximately 1.5s
    await waitFor(() => {
      expect(screen.getByText(/1\.[4-6]s/)).toBeInTheDocument();
    });
  });

  it('hides timer when loading is false', () => {
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    // Timer should not be visible when not loading
    expect(screen.queryByText(/\d+\.\d+s/)).not.toBeInTheDocument();
  });

  it('resets timer when search starts again', async () => {
    const { rerender } = render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={true}
        onClear={mockOnClear}
      />
    );

    // Advance timer
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    // Stop loading
    rerender(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    // Start loading again
    rerender(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={true}
        onClear={mockOnClear}
      />
    );

    // Timer should reset to 0.0s
    expect(screen.getByText('0.0s')).toBeInTheDocument();
  });
});
