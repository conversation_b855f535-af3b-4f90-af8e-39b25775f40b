import React from 'react';
import { render, screen } from '@testing-library/react';
import { MarkdownRenderer } from '../webview/frontend/src/components/ui/MarkdownRenderer';

describe('MarkdownRenderer', () => {
  it('renders simple markdown text', () => {
    const content = '# Hello World\n\nThis is a **bold** text.';
    render(<MarkdownRenderer content={content} />);
    
    expect(screen.getByText('Hello World')).toBeInTheDocument();
    expect(screen.getByText('bold')).toBeInTheDocument();
  });

  it('renders code blocks with syntax highlighting', () => {
    const content = '```javascript\nconst hello = "world";\n```';
    render(<MarkdownRenderer content={content} />);
    
    // Check if code block is rendered
    const codeElement = screen.getByText('const hello = "world";');
    expect(codeElement).toBeInTheDocument();
  });

  it('renders lists correctly', () => {
    const content = '- Item 1\n- Item 2\n- Item 3';
    render(<MarkdownRenderer content={content} />);
    
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('renders links with target="_blank"', () => {
    const content = '[Google](https://google.com)';
    render(<MarkdownRenderer content={content} />);
    
    const link = screen.getByRole('link', { name: 'Google' });
    expect(link).toHaveAttribute('href', 'https://google.com');
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('applies custom className', () => {
    const content = '# Test';
    const { container } = render(<MarkdownRenderer content={content} className="custom-class" />);
    
    expect(container.firstChild).toHaveClass('custom-class');
  });
});
