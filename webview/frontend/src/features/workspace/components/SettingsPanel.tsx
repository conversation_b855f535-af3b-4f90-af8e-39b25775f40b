import { Select } from '@/components/ui';
import { Settings } from 'lucide-react';

interface SettingsPanelProps {
  searchType: 'grep' | 'embedding';
  onSearchTypeChange: (type: 'grep' | 'embedding') => void;
}

export function SettingsPanel({ searchType, onSearchTypeChange }: SettingsPanelProps) {
  const searchTypeOptions = [
    { label: 'Grep 搜索', value: 'grep' as const },
    { label: '语义搜索 (Embedding)', value: 'embedding' as const },
  ];

  const handleReset = () => {
    onSearchTypeChange('grep');
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <Settings className="h-4 w-4" />
        搜索设置
      </div>

      <div className="space-y-3">
        <Select
          label="搜索类型"
          options={searchTypeOptions}
          value={searchType}
          onChange={onSearchTypeChange}
        />
      </div>

      <div className="text-xs text-gray-500 space-y-1">
        <div>• Grep: 基于文本匹配的快速搜索</div>
        <div>• Embedding: 基于语义理解的智能搜索</div>
      </div>
    </div>
  );
}
