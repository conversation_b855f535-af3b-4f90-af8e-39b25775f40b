import React, { useState } from 'react';
import { Button, Input, Markdown<PERSON>enderer } from '@/components/ui';
import { Search, X } from 'lucide-react';
import { useWorkspace } from '@/features/workspace/hooks';
import { useAppStore } from '@/store/appStore';
import type { SearchRequest } from '@/types';

interface SearchPanelProps {
  onSearch: (request: SearchRequest) => void;
  loading?: boolean;
  onClear?: () => void;
  searchResult?: string;
  searchError?: string | null;
  searchTime?: number;
}

export function SearchPanel({
  onSearch,
  loading = false,
  onClear,
  searchResult,
  searchError,
  searchTime
}: SearchPanelProps) {
  const [query, setQuery] = useState('');
  const { getCurrentWorkspace } = useWorkspace();
  const { searchType } = useAppStore();

  const currentWorkspace = getCurrentWorkspace;

  const handleSearch = () => {
    if (!query.trim() || !currentWorkspace) return;

    const request: SearchRequest = {
      query: query.trim(),
      workspaceId: currentWorkspace.id,
      searchType, // 使用全局设置中的searchType
      options: {
        maxResults: 50,
      },
    };

    onSearch(request);
  };

  const handleClear = () => {
    setQuery('');
    onClear?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSearch();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setQuery(e.target.value);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-gray-900">搜索查询</h3>
        {onClear && (
          <Button
            variant="ghost"
            size="sm"
            icon={<X className="h-3 w-3" />}
            onClick={handleClear}
          >
            清空
          </Button>
        )}
      </div>

      <div className="space-y-3">
        <Input
          type="textarea"
          placeholder="输入要搜索的内容...&#10;支持多行输入，按 Enter 搜索"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={loading}
        />

        <Button
          variant="primary"
          size="md"
          icon={<Search className="h-4 w-4" />}
          onClick={handleSearch}
          loading={loading}
          disabled={!query.trim() || !currentWorkspace || loading}
          className="w-full"
        >
          {loading ? '搜索中...' : '开始搜索'}
        </Button>
      </div>

      {!currentWorkspace && (
        <div className="text-sm text-warning-500 bg-warning-50 p-3 rounded-md">
          请先选择一个工作区
        </div>
      )}

      {/* 搜索结果区域 */}
      {(searchResult || searchError) && (
        <div className="border-t border-gray-200 pt-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900">搜索结果</h3>
            {searchTime !== undefined && searchTime > 0 && (
              <span className="text-xs text-gray-500">
                耗时: {searchTime.toFixed(2)}ms
              </span>
            )}
          </div>

          {searchError && (
            <div className="text-sm text-error-500 bg-error-50 p-3 rounded-md mb-3">
              {searchError}
            </div>
          )}

          {searchResult && (
            <div className="bg-gray-50 rounded-md p-3 max-h-96 overflow-y-auto">
              <MarkdownRenderer
                content={searchResult}
                className="text-sm"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}
