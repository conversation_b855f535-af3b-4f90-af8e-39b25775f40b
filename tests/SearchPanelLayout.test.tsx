import React from 'react';
import { render, screen } from '@testing-library/react';
import { SearchPanel } from '../webview/frontend/src/features/search/components/SearchPanel';

// Mock the cn utility
jest.mock('../webview/frontend/src/utils/cn', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
}));

// Mock the hooks
jest.mock('../webview/frontend/src/features/workspace/hooks', () => ({
  useWorkspace: () => ({
    getCurrentWorkspace: {
      id: 'test-workspace',
      name: 'Test Workspace',
      path: '/test/path'
    }
  })
}));

jest.mock('../webview/frontend/src/store/appStore', () => ({
  useAppStore: () => ({
    searchType: 'grep' as const
  })
}));

describe('SearchPanel Layout', () => {
  const mockOnSearch = jest.fn();
  const mockOnClear = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('has correct flex layout structure', () => {
    const { container } = render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    // Main container should have flex column layout
    const mainContainer = container.firstChild as HTMLElement;
    expect(mainContainer).toHaveClass('h-full', 'flex', 'flex-col');
  });

  it('search form area has flex-shrink-0 to maintain fixed height', () => {
    const { container } = render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    // Find the search form container
    const searchFormContainer = container.querySelector('.flex-shrink-0');
    expect(searchFormContainer).toBeInTheDocument();
  });

  it('search results area uses flex-1 to fill remaining space', () => {
    const searchResult = '# Test Results\n\nSome search results here.';
    
    const { container } = render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
        searchResult={searchResult}
      />
    );

    // Find the search results container
    const resultsContainer = container.querySelector('.flex-1.flex.flex-col');
    expect(resultsContainer).toBeInTheDocument();
  });

  it('markdown content area has proper overflow handling', () => {
    const longSearchResult = '# Test Results\n\n' + 'This is a very long line that should cause overflow. '.repeat(100);
    
    const { container } = render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
        searchResult={longSearchResult}
      />
    );

    // Find the markdown content container
    const markdownContainer = container.querySelector('.overflow-y-auto.min-h-0');
    expect(markdownContainer).toBeInTheDocument();
  });

  it('shows workspace warning with proper layout', () => {
    // Mock no workspace
    jest.doMock('../webview/frontend/src/features/workspace/hooks', () => ({
      useWorkspace: () => ({
        getCurrentWorkspace: null
      })
    }));

    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    const warningElement = screen.getByText('请先选择一个工作区');
    expect(warningElement).toBeInTheDocument();
    
    // Warning should have flex-shrink-0 class
    const warningContainer = warningElement.parentElement;
    expect(warningContainer).toHaveClass('flex-shrink-0');
  });

  it('error message has proper layout classes', () => {
    const searchError = 'Search failed: Network error';
    
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
        searchError={searchError}
      />
    );

    const errorElement = screen.getByText(searchError);
    expect(errorElement).toBeInTheDocument();
    
    // Error container should have flex-shrink-0 class
    const errorContainer = errorElement.parentElement;
    expect(errorContainer).toHaveClass('flex-shrink-0');
  });
});
