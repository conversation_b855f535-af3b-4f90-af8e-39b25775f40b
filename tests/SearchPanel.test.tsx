import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SearchPanel } from '../webview/frontend/src/features/search/components/SearchPanel';
import type { SearchRequest } from '../webview/frontend/src/types';

// Mock the hooks
jest.mock('../webview/frontend/src/features/workspace/hooks', () => ({
  useWorkspace: () => ({
    getCurrentWorkspace: {
      id: 'test-workspace',
      name: 'Test Workspace',
      path: '/test/path'
    }
  })
}));

jest.mock('../webview/frontend/src/store/appStore', () => ({
  useAppStore: () => ({
    searchType: 'grep' as const
  })
}));

describe('SearchPanel', () => {
  const mockOnSearch = jest.fn();
  const mockOnClear = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders search input and button', () => {
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    expect(screen.getByPlaceholderText(/输入要搜索的内容/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /开始搜索/ })).toBeInTheDocument();
  });

  it('calls onSearch when search button is clicked', async () => {
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    const input = screen.getByPlaceholderText(/输入要搜索的内容/);
    const searchButton = screen.getByRole('button', { name: /开始搜索/ });

    fireEvent.change(input, { target: { value: 'test query' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        query: 'test query',
        workspaceId: 'test-workspace',
        searchType: 'grep',
        options: {
          maxResults: 50,
        },
      });
    });
  });

  it('displays search results when provided', () => {
    const searchResult = '# Search Results\n\nFound **3** matches.';
    
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
        searchResult={searchResult}
        searchTime={123.45}
      />
    );

    expect(screen.getByText('搜索结果')).toBeInTheDocument();
    expect(screen.getByText('耗时: 123.45ms')).toBeInTheDocument();
    expect(screen.getByText('Search Results')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('displays error message when search fails', () => {
    const searchError = 'Search failed: Network error';
    
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
        searchError={searchError}
      />
    );

    expect(screen.getByText('搜索结果')).toBeInTheDocument();
    expect(screen.getByText(searchError)).toBeInTheDocument();
  });

  it('shows loading state when searching', () => {
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={true}
        onClear={mockOnClear}
      />
    );

    expect(screen.getByRole('button', { name: /搜索中/ })).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/输入要搜索的内容/)).toBeDisabled();
  });

  it('calls onClear when clear button is clicked', () => {
    render(
      <SearchPanel
        onSearch={mockOnSearch}
        loading={false}
        onClear={mockOnClear}
      />
    );

    const clearButton = screen.getByRole('button', { name: /清空/ });
    fireEvent.click(clearButton);

    expect(mockOnClear).toHaveBeenCalled();
  });
});
